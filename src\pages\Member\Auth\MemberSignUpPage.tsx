import * as yup from "yup";
import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useToast } from "@/hooks/useToast";
import { RoleEnum, ToastStatusEnum } from "@/utils/Enums";
import { LazyLoad } from "@/components/LazyLoad";
import { useSDK } from "@/hooks/useSDK";
import { MkdPasswordInput } from "@/components/MkdPasswordInput";
import MkdInputV2 from "@/components/MkdInputV2";
import { useUpdateModelMutation } from "@/pages/Member/updateModel";
import { Models } from "@/utils/baas";
import { useContexts } from "@/hooks/useContexts";
import mockData from "@/utils/mockData/memberSignup.json";

interface MemberSignUpPageProps {
  role?: string;
}

const MemberSignUpPage = ({
  role: _role = RoleEnum.USER,
}: MemberSignUpPageProps) => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const { authDispatch: dispatch, showToast } = useContexts();
  const { mutateAsync: updateModel } = useUpdateModelMutation(
    Models.USER,
    "member"
  );
  const [searchParams] = useSearchParams();

  const [submitLoading, setSubmitLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [referralType, setReferralType] = useState<"code" | "link" | "none">(
    "none"
  );
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [referralCodeValid, setReferralCodeValid] = useState<boolean | null>(
    null
  );
  const [referralCodeValidating, setReferralCodeValidating] = useState(false);
  const [referrerName, setReferrerName] = useState("");

  // New state variables for two-phase signup
  const [registeredUser, setRegisteredUser] = useState<any>(null);
  const [profileUpdated, setProfileUpdated] = useState(false);

  const navigate = useNavigate();

  const schema = yup
    .object({
      fullName: yup.string().required("Full name is required"),
      email: yup.string().email("Invalid email").required("Email is required"),
      phoneNumber: yup.string().required("Phone number is required"),
      password: yup
        .string()
        .min(8, "Password must be at least 8 characters")
        .required("Password is required"),
      referralCode: yup.string().when("referralType", {
        is: "code",
        then: (schema) => schema.required("Referral code is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const handlePrefill = () => {
    const randomUser = mockData[Math.floor(Math.random() * mockData.length)];
    const randomSuffix = Math.floor(Math.random() * 1000);
    setValue("fullName", randomUser.fullName);
    setValue("email", `${randomUser.email}+${randomSuffix}@example.com`);
    setValue("phoneNumber", randomUser.phoneNumber);
    setValue("password", randomUser.password);

    if (randomUser.referralCode) {
      setReferralType("code");
      setValue("referralCode", randomUser.referralCode);
    } else {
      const referralTypes = ["link", "none"];
      const randomReferralType = referralTypes[
        Math.floor(Math.random() * referralTypes.length)
      ] as "link" | "none";
      setReferralType(randomReferralType);
      if (randomReferralType === "link") {
        setValue("referralCode", "REFLINK123");
        setReferrerName("Referred by Link");
      } else {
        setValue("referralCode", "");
      }
    }

    setTermsAccepted(true);
    setPrivacyAccepted(true);
  };

  const watchedReferralCode = watch("referralCode");
  const memberData = watch();
  const { fullName, phoneNumber } = memberData;

  // Check for referral code in URL parameters
  useEffect(() => {
    const refParam = searchParams.get("ref");
    if (refParam) {
      setReferralType("link");
      setValue("referralCode", refParam);
      validateReferralCode(refParam);
    }
  }, [searchParams, setValue]);

  // Validate referral code when it changes
  useEffect(() => {
    if (
      referralType === "code" &&
      watchedReferralCode &&
      watchedReferralCode.length >= 6
    ) {
      validateReferralCode(watchedReferralCode);
    } else if (!watchedReferralCode) {
      setReferralCodeValid(null);
      setReferrerName("");
    }
  }, [watchedReferralCode, referralType]);

  const validateReferralCode = useCallback(
    async (code: string) => {
      if (!code || code.length < 6) return;

      setReferralCodeValidating(true);
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/signup/validate-referral",
          method: "POST",
          body: { referralCode: code },
        });

        console.log("Validate Referral Code API Response:", response);

        if (!response.error) {
          setReferralCodeValid(true);
          setReferrerName(String(response.data?.referrerName || ""));
        } else {
          setReferralCodeValid(false);
          setReferrerName("");
          setError("referralCode", {
            message: String(response.message || "Invalid referral code"),
          });
        }
      } catch (error: any) {
        console.error("Error validating referral code:", error);
        setReferralCodeValid(false);
        setReferrerName("");
        setError("referralCode", {
          message: String(error?.message || "Failed to validate referral code"),
        });
      } finally {
        setReferralCodeValidating(false);
      }
    },
    [setError]
  );

  // Profile update function (similar to trainer signup)
  const updateProfile = async () => {
    try {
      // Split full name into first and last name
      const nameParts = fullName.trim().split(" ");
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(" ") || "";

      await updateModel({
        id: registeredUser?.user_id || 11,
        payload: {
          verify: true,
          data: JSON.stringify({
            first_name: firstName,
            last_name: lastName,
            phone: phoneNumber,
            referral_code: referralType !== "none" ? watchedReferralCode : null,
            referral_type: referralType,
            terms_accepted: termsAccepted,
            privacy_accepted: privacyAccepted,
            referrer_name: referrerName,
          }),
        },
      });
      setProfileUpdated(true);
    } catch (e: any) {
      showToast("Profile update failed", 4000, ToastStatusEnum.ERROR);
      setSubmitLoading(false);
      console.error("Profile update error:", e);
    }
  };

  const testProfile = () => {
    updateProfile();
  };

  // Effect: after LOGIN, update profile
  useEffect(() => {
    if (registeredUser && registeredUser.user_id && !profileUpdated) {
      updateProfile();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [registeredUser, updateModel, profileUpdated]);

  // Effect: after profile update, navigate
  useEffect(() => {
    if (profileUpdated) {
      success("Account created successfully! Redirecting to dashboard...");
      navigate("/member/dashboard");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profileUpdated]);

  // Update loading state when profile is being updated
  useEffect(() => {
    if (registeredUser && !profileUpdated) {
      setSubmitLoading(true);
    } else if (profileUpdated) {
      setSubmitLoading(false);
    }
  }, [registeredUser, profileUpdated]);

  const onSubmit = useCallback(
    async (data: yup.InferType<typeof schema>) => {
      if (!termsAccepted) {
        showError("Please accept the terms of service");
        return;
      }

      if (!privacyAccepted) {
        showError("Please accept the privacy policy");
        return;
      }

      // Validate referral code if provided
      if (
        referralType === "code" &&
        data.referralCode &&
        referralCodeValid !== true
      ) {
        showError("Please enter a valid referral code");
        return;
      }

      try {
        setSubmitLoading(true);
        setApiError(null);

        // Phase 1: Register user using SDK register method
        const result: any = await sdk.register(
          data.email,
          data.password,
          "member"
        );

        if (!result.error) {
          setRegisteredUser(result); // Save for useEffect
          dispatch({ type: "LOGIN", payload: result as any });
          showToast("Successfully Registered", 4000, ToastStatusEnum.SUCCESS);
        } else {
          setSubmitLoading(false);
          if (result.validation) {
            const keys = Object.keys(result.validation);
            for (let i = 0; i < keys.length; i++) {
              const field = keys[i];
              setError(field as any, {
                type: "manual",
                message: result.validation[field],
              });
            }
          } else {
            setApiError(String(result.message || "Failed to create account"));
            showError(String(result.message || "Failed to create account"));
          }
        }
      } catch (error: any) {
        console.error("Error during signup:", error);
        const errorMessage = String(
          error?.response?.data?.message || error?.message || "Signup failed"
        );
        setApiError(errorMessage);
        showError(errorMessage);
        setSubmitLoading(false);
      }
    },
    [
      termsAccepted,
      privacyAccepted,
      referralType,
      referralCodeValid,

      dispatch,
      showToast,
      showError,
      setError,
    ]
  );

  return (
    <main className="flex h-screen">
      {/* Left Sidebar */}
      <div className="w-[250px] bg-[#0F2C59] flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-600">
          <h1 className="text-2xl font-bold">
            <span style={{ color: "#E63946" }}>eBa</span>
            <span className="text-white">Dollar</span>
          </h1>
        </div>

        {/* Progress Steps */}
        <div className="p-6 flex-1">
          <div className="space-y-6">
            {/* Basic Information Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#E63946] flex items-center justify-center">
                <span className="text-white text-xs font-bold">1</span>
              </div>
              <div>
                <div className="text-white font-medium">Basic Information</div>
                <div className="text-gray-300 text-sm">50% Complete</div>
              </div>
            </div>

            {/* Verify Identity Step */}
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full border-2 border-gray-500 flex items-center justify-center">
                <span className="text-gray-500 text-xs font-bold">2</span>
              </div>
              <div>
                <div className="text-gray-500 font-medium">Verify Identity</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white flex items-start justify-center p-8 h-full overflow-y-auto border border-red-500">
        <div className="w-full max-w-xl">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Create your account
            </h2>
            <p className="text-gray-600">
              Get started now and complete the remaining steps later.
            </p>
          </div>

          {/* Error Display */}
          {apiError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <div className="text-red-400 mr-3">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-red-800">
                    Signup Failed
                  </h3>
                  <p className="text-sm text-red-700 mt-1">{apiError}</p>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-[#E63946]">
                  Basic Information
                </span>
                <span className="text-sm text-[#E63946]">50% Complete</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-[#E63946] h-2 rounded-full"
                  style={{ width: "50%" }}
                ></div>
              </div>
            </div>

            {/* Basic Info Section */}
            <div className="space-y-4">
              <div className="text-sm font-medium text-gray-700 mb-3">
                Basic Info
              </div>

              {/* Full Name */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="fullName"
                    type="text"
                    register={register}
                    errors={errors}
                    required
                    placeholder="Enter your full name"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Full Name*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Email Address */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="email"
                    type="email"
                    register={register}
                    errors={errors}
                    required
                    placeholder="<EMAIL>"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Email Address*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Phone Number */}
              <div>
                <LazyLoad>
                  <MkdInputV2
                    name="phoneNumber"
                    type="tel"
                    register={register}
                    errors={errors}
                    required
                    placeholder="(*************"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-medium text-gray-700">
                        Phone Number*
                      </MkdInputV2.Label>
                      <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </LazyLoad>
              </div>

              {/* Password */}
              <div>
                <LazyLoad>
                  <MkdPasswordInput
                    required
                    name="password"
                    label="Password*"
                    errors={errors}
                    register={register}
                    inputClassName="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]"
                    placeholder="Create a strong password"
                    labelClassName="text-sm font-medium text-gray-700"
                  />
                </LazyLoad>
              </div>

              <p className="text-xs text-gray-500">
                Must be at least 8 characters with uppercase, lowercase and
                numbers
              </p>
            </div>

            {/* Referral Information */}
            <div className="bg-blue-50 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">🎁</span>
                </div>
                <h3 className="text-sm font-medium text-gray-900">
                  Referral Information
                </h3>
              </div>

              {/* Referral Options */}
              <div className="space-y-3">
                {/* I have a referral code */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="referral-code"
                    name="referralType"
                    value="code"
                    checked={referralType === "code"}
                    onChange={(e) => setReferralType(e.target.value as "code")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-code"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I have a referral code
                    </label>
                    <p className="text-xs text-gray-600">
                      Enter the code shared by someone who referred you
                    </p>

                    {referralType === "code" && (
                      <div className="mt-2">
                        <LazyLoad>
                          <MkdInputV2
                            name="referralCode"
                            type="text"
                            register={register}
                            errors={errors}
                            placeholder="Enter referral code"
                          >
                            <MkdInputV2.Container>
                              <MkdInputV2.Field className="!bg-white !border-gray-300 !text-gray-900 focus:!border-[#E63946] focus:!ring-[#E63946]" />
                              <MkdInputV2.Error />
                            </MkdInputV2.Container>
                          </MkdInputV2>
                        </LazyLoad>

                        {/* Referral Code Validation Feedback */}
                        {watchedReferralCode &&
                          watchedReferralCode.length >= 6 && (
                            <div className="mt-2">
                              {referralCodeValidating && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                                  Validating referral code...
                                </div>
                              )}
                              {!referralCodeValidating &&
                                referralCodeValid === true && (
                                  <div className="flex items-center text-sm text-green-600">
                                    <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                      <span className="text-white text-xs">
                                        ✓
                                      </span>
                                    </div>
                                    Valid referral code from {referrerName}
                                  </div>
                                )}
                              {!referralCodeValidating &&
                                referralCodeValid === false && (
                                  <div className="flex items-center text-sm text-red-600">
                                    <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center mr-2">
                                      <span className="text-white text-xs">
                                        ✗
                                      </span>
                                    </div>
                                    Invalid referral code
                                  </div>
                                )}
                            </div>
                          )}
                      </div>
                    )}
                  </div>
                </div>

                {/* I clicked a referral link */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="referral-link"
                    name="referralType"
                    value="link"
                    checked={referralType === "link"}
                    onChange={(e) => setReferralType(e.target.value as "link")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="referral-link"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      I clicked a referral link
                    </label>
                    <p className="text-xs text-gray-600">
                      The referral information has been automatically detected
                    </p>

                    {referralType === "link" && (
                      <div className="mt-2 flex items-center space-x-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-sm text-green-600">
                          {referrerName || "Referral detected"}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* No referral */}
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="no-referral"
                    name="referralType"
                    value="none"
                    checked={referralType === "none"}
                    onChange={(e) => setReferralType(e.target.value as "none")}
                    className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor="no-referral"
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      No referral
                    </label>
                    <p className="text-xs text-gray-600">
                      I found eBaDollar on my own
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-100 rounded-md p-3">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                  <p className="text-xs text-blue-800">
                    <strong>Referral Bonus:</strong> Both you and your referrer
                    will receive rewards when you complete your first
                    transaction!
                  </p>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="terms"
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  I have read and accepted the{" "}
                  <Link to="/terms" className="text-[#E63946] hover:underline">
                    Terms of service
                  </Link>
                </label>
              </div>

              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="privacy"
                  checked={privacyAccepted}
                  onChange={(e) => setPrivacyAccepted(e.target.checked)}
                  className="mt-1 w-4 h-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                />
                <label
                  htmlFor="privacy"
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  I have read and accepted the{" "}
                  <Link
                    to="/privacy"
                    className="text-[#E63946] hover:underline"
                  >
                    Privacy policy
                  </Link>
                </label>
              </div>
            </div>

            {/* Verification Notice */}
            <div className="bg-blue-50 rounded-md p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-white text-xs">ℹ</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    Complete verification now or later
                  </p>
                  <p className="text-xs text-blue-700">
                    You can start using the application immediately after this
                    step. We will remind you to complete identity verification
                    later.
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#E63946]"
                onClick={() => navigate("/member/login")}
              >
                Cancel
              </button>
              <InteractiveButton
                type="button"
                className="flex-1 !bg-blue-500 hover:!bg-blue-600 focus:!ring-blue-500 !py-3 !font-medium"
                onClick={handlePrefill}
              >
                Prefill Form
              </InteractiveButton>

              <InteractiveButton
                type="submit"
                className="flex-1 !bg-[#E63946] hover:!bg-[#d63384] focus:!ring-[#E63946] !py-3 !font-medium"
                loading={submitLoading}
                disabled={submitLoading}
              >
                {registeredUser && !profileUpdated
                  ? "Setting up profile..."
                  : "Create Account & Continue"}
              </InteractiveButton>
            </div>
          </form>
          <p className="text-sm text-center text-gray-600 mt-6">
            Test profile
            <div
              onClick={() => {
                testProfile();
              }}
              className="font-medium text-[#E63946] hover:opacity-80 cursor-pointer"
            >
              Click me
            </div>
          </p>

          {/* Login Link */}
          <p className="text-sm text-center text-gray-600 mt-6">
            Already have an account?{" "}
            <Link
              to="/member/login"
              className="font-medium text-[#E63946] hover:opacity-80"
            >
              Log In
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
};

export default MemberSignUpPage;
